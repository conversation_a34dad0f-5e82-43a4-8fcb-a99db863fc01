# 传输问题排查指南

## 🔍 问题诊断

基于调试结果，压缩和解压缩功能本身是正常的。如果从Linux服务器传输到Windows端看不到文件，可能的原因包括：

### 1. 目标路径问题

**问题**: 文件可能被解压到了错误的目录
**解决方案**:
- 确保目标路径是一个**存在的目录**
- 避免使用文件名作为目标路径
- 使用绝对路径而不是相对路径

**正确示例**:
```
✓ C:\Users\<USER>\Documents\备份文件夹
✓ D:\备份\服务器数据
✗ C:\Users\<USER>\Documents\备份文件.tar.gz  (这是文件名，不是目录)
✗ 备份文件夹  (相对路径可能导致混淆)
```

### 2. 权限问题

**问题**: Windows目标目录没有写入权限
**解决方案**:
- 选择有写入权限的目录（如用户文档文件夹）
- 避免选择系统目录（如C:\Program Files）
- 以管理员身份运行程序（如果必要）

### 3. 路径编码问题

**问题**: 中文路径或特殊字符导致的编码问题
**解决方案**:
- 避免在路径中使用特殊字符
- 使用英文路径进行测试
- 确保路径不包含空格或特殊符号

## 🛠️ 排查步骤

### 步骤1: 检查传输日志
1. 在程序界面查看"操作日志"区域
2. 查找错误信息或异常提示
3. 注意压缩、传输、解压各阶段的状态

### 步骤2: 验证目标目录
1. 确保目标路径存在且可写
2. 手动在目标目录创建测试文件验证权限
3. 检查目录路径是否正确（没有拼写错误）

### 步骤3: 使用简单路径测试
1. 创建简单的测试目录：`C:\test_backup`
2. 使用英文路径，避免中文和特殊字符
3. 先传输小文件进行测试

### 步骤4: 检查压缩选项
1. 尝试禁用压缩进行传输
2. 对比启用/禁用压缩的结果
3. 查看是否是压缩相关的问题

## 🔧 常见问题解决

### 问题1: 传输完成但找不到文件

**可能原因**: 文件被解压到了意外的位置

**解决方法**:
1. 检查目标路径设置是否正确
2. 在Windows资源管理器中搜索最近创建的文件
3. 查看程序日志中的实际解压路径

**调试命令**:
```bash
# 在目标目录搜索最近的文件
dir /s /od C:\目标目录
```

### 问题2: 权限被拒绝

**可能原因**: 目标目录没有写入权限

**解决方法**:
1. 选择用户目录下的文件夹（如Documents）
2. 右键目标文件夹 → 属性 → 安全 → 检查权限
3. 以管理员身份运行程序

### 问题3: 路径包含中文字符

**可能原因**: 编码问题导致路径解析错误

**解决方法**:
1. 使用英文路径进行测试
2. 避免路径中的空格和特殊字符
3. 使用短路径名称

## 📝 推荐的测试流程

### 1. 基础连接测试
```
1. 添加服务器配置
2. 测试SSH连接
3. 浏览远程目录确认可访问
```

### 2. 简单文件传输测试
```
源端: 远程服务器上的单个小文件
目标: C:\test_backup (新建的简单目录)
选项: 禁用压缩
```

### 3. 压缩传输测试
```
源端: 远程服务器上的目录
目标: C:\test_backup
选项: 启用压缩
```

### 4. 复杂场景测试
```
源端: 包含中文文件名的目录
目标: 用户文档目录
选项: 启用压缩 + 验证
```

## 🚨 紧急排查

如果仍然找不到传输的文件，请按以下步骤操作：

### 1. 立即检查
```bash
# 检查最近创建的文件
forfiles /p C:\ /s /m *.* /d +0 /c "cmd /c echo @path @fdate @ftime"
```

### 2. 查看临时目录
检查以下目录是否有残留文件：
- `%TEMP%\backup_tool_*`
- `C:\Users\<USER>\AppData\Local\Temp\`

### 3. 启用详细日志
在程序中查看详细的操作日志，特别关注：
- "解压完成" 消息
- 实际的解压路径
- 任何错误或警告信息

## 💡 最佳实践

1. **使用绝对路径**: 始终使用完整的绝对路径
2. **避免特殊字符**: 路径中不要包含中文、空格、特殊符号
3. **权限检查**: 确保目标目录有写入权限
4. **分步测试**: 先测试小文件，再测试大目录
5. **查看日志**: 始终检查程序的操作日志
6. **备份重要数据**: 传输前确保源数据有备份

## 📞 获取支持

如果问题仍然存在，请提供以下信息：
1. 源路径和目标路径的具体设置
2. 程序操作日志的完整内容
3. 是否启用了压缩选项
4. Windows版本和权限设置
5. 错误信息的截图

通过这些信息可以更准确地诊断和解决问题。
