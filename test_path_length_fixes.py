#!/usr/bin/env python3
"""
测试路径长度修复
验证Windows路径长度限制问题的解决方案
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_path_length_handler():
    """测试路径长度处理器"""
    print("=== 测试路径长度处理器 ===")
    
    try:
        from path_length_utils import WindowsPathLengthHandler
        
        handler = WindowsPathLengthHandler()
        
        # 测试长路径检测
        short_path = "C:\\short\\path"
        long_path = "C:\\" + "very_long_directory_name\\" * 20 + "file.txt"
        
        print(f"短路径长度: {len(short_path)} - 是否过长: {handler.is_path_too_long(short_path)}")
        print(f"长路径长度: {len(long_path)} - 是否过长: {handler.is_path_too_long(long_path)}")
        
        # 测试文件名截断
        long_filename = "this_is_a_very_long_filename_that_exceeds_the_normal_limits_and_should_be_truncated_properly.txt"
        truncated = handler.truncate_filename(long_filename)
        
        print(f"原文件名长度: {len(long_filename)}")
        print(f"截断后长度: {len(truncated)}")
        print(f"截断后文件名: {truncated}")
        
        # 测试路径组件缩短
        long_component = "this_is_a_very_long_directory_component_name_that_should_be_shortened"
        short_component = handler.create_short_path_component(long_component, 30)
        
        print(f"原组件: {long_component}")
        print(f"短组件: {short_component}")
        
        print("✅ 路径长度处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 路径长度处理器测试失败: {e}")
        return False

def test_backup_path_optimization():
    """测试备份路径优化"""
    print("\n=== 测试备份路径优化 ===")
    
    try:
        from path_length_utils import WindowsPathLengthHandler
        
        handler = WindowsPathLengthHandler()
        
        # 模拟实际的长路径场景
        base_backup = "G:\\tyy_backup\\backup_20250708_114433"
        long_source_paths = [
            "/imagefile/dolfinx/volume-append/jupyter-dolfinx/very/long/nested/directory/structure/with/many/levels",
            "/home/<USER>/documents/projects/very_long_project_name_with_many_words/src/components/ui/forms",
            "/var/log/application/detailed_logs/year_2024/month_12/daily_logs/application_specific_logs"
        ]
        
        for source_path in long_source_paths:
            optimized_path, mapping = handler.optimize_backup_path(base_backup, source_path)
            
            print(f"原始路径: {source_path}")
            print(f"优化路径: {optimized_path}")
            print(f"路径长度: {len(optimized_path)}")
            print(f"是否安全: {not handler.is_path_too_long(optimized_path)}")
            print(f"短目录名: {mapping['short_dirname']}")
            print("---")
        
        print("✅ 备份路径优化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 备份路径优化测试失败: {e}")
        return False

def test_safe_extraction_path():
    """测试安全解压路径"""
    print("\n=== 测试安全解压路径 ===")
    
    try:
        from path_length_utils import WindowsPathLengthHandler
        
        handler = WindowsPathLengthHandler()
        
        base_path = "G:\\tyy_backup\\backup_20250708_114433\\imagefile"
        
        # 模拟tar文件中的长路径
        long_relative_paths = [
            "dolfinx/volume-append/jupyter-dolfinx /Untitled.ipynb",
            "very/deep/nested/directory/structure/with/many/levels/and/long/names/file.txt",
            "project/src/components/ui/forms/very_long_form_component_name.jsx"
        ]
        
        for relative_path in long_relative_paths:
            safe_path = handler.get_safe_extraction_path(base_path, relative_path)
            
            print(f"相对路径: {relative_path}")
            print(f"完整原路径: {os.path.join(base_path, relative_path)}")
            print(f"原路径长度: {len(os.path.join(base_path, relative_path))}")
            print(f"安全路径: {safe_path}")
            print(f"安全路径长度: {len(safe_path)}")
            print(f"是否安全: {not handler.is_path_too_long(safe_path)}")
            print("---")
        
        print("✅ 安全解压路径测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 安全解压路径测试失败: {e}")
        return False

def test_compression_manager_integration():
    """测试压缩管理器集成"""
    print("\n=== 测试压缩管理器集成 ===")
    
    try:
        from compression.tar_compression import TarCompressionManager
        
        # 创建压缩管理器
        compression_manager = TarCompressionManager()
        
        # 检查是否正确集成了路径处理器
        assert hasattr(compression_manager, 'path_handler'), "压缩管理器缺少path_handler属性"
        assert hasattr(compression_manager.path_handler, 'is_path_too_long'), "路径处理器缺少is_path_too_long方法"
        assert hasattr(compression_manager.path_handler, 'get_safe_extraction_path'), "路径处理器缺少get_safe_extraction_path方法"
        
        print("✅ 压缩管理器集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 压缩管理器集成测试失败: {e}")
        return False

def test_backup_manager_integration():
    """测试备份管理器集成"""
    print("\n=== 测试备份管理器集成 ===")
    
    try:
        from backup_manager import BackupManager
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        backup_manager = BackupManager(config_manager)
        
        # 检查是否正确集成了路径处理器
        assert hasattr(backup_manager, 'path_handler'), "备份管理器缺少path_handler属性"
        
        # 测试路径优化功能
        base_backup = "C:\\Backups\\test"
        long_source = "/very/long/path/that/might/cause/issues/on/windows/systems"
        
        optimized_path, mapping = backup_manager.path_handler.optimize_backup_path(base_backup, long_source)
        
        print(f"优化路径: {optimized_path}")
        print(f"路径映射: {mapping}")
        
        print("✅ 备份管理器集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 备份管理器集成测试失败: {e}")
        return False

def test_windows_path_limits():
    """测试Windows路径限制检测"""
    print("\n=== 测试Windows路径限制检测 ===")
    
    try:
        from path_length_utils import WindowsPathLengthHandler
        
        # 测试具体的问题路径
        problem_path = "G:\\tyy_backup\\backup_20250708_114433\\imagefile\\imagefile\\dolfinx\\volume-append\\jupyter-dolfinx \\Untitled.ipynb"
        
        handler = WindowsPathLengthHandler()
        
        print(f"问题路径: {problem_path}")
        print(f"路径长度: {len(problem_path)}")
        print(f"是否超过限制: {handler.is_path_too_long(problem_path)}")
        print(f"Windows限制: {handler.MAX_PATH_LENGTH}")
        
        # 测试路径优化
        base_path = "G:\\tyy_backup\\backup_20250708_114433"
        relative_path = "imagefile/dolfinx/volume-append/jupyter-dolfinx /Untitled.ipynb"
        
        safe_path = handler.get_safe_extraction_path(base_path, relative_path)
        print(f"优化后路径: {safe_path}")
        print(f"优化后长度: {len(safe_path)}")
        print(f"是否安全: {not handler.is_path_too_long(safe_path)}")
        
        print("✅ Windows路径限制检测测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Windows路径限制检测测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Windows路径长度修复...")
    print("=" * 60)
    
    tests = [
        test_path_length_handler,
        test_backup_path_optimization,
        test_safe_extraction_path,
        test_compression_manager_integration,
        test_backup_manager_integration,
        test_windows_path_limits
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有路径长度修复测试通过！")
        print("\n修复功能:")
        print("1. ✅ 路径长度检测 - 自动检测超过260字符的路径")
        print("2. ✅ 文件名截断 - 智能截断长文件名并保持唯一性")
        print("3. ✅ 路径优化 - 创建短路径组件避免长度限制")
        print("4. ✅ 安全解压 - 解压时自动处理长路径问题")
        print("5. ✅ 路径映射 - 记录原始路径和优化路径的对应关系")
        print("\n现在应该可以正确处理包含长路径的备份了！")
    else:
        print("⚠️  部分路径长度修复测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
