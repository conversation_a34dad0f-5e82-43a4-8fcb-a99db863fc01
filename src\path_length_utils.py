"""
Windows路径长度处理工具
解决Windows 260字符路径长度限制问题
"""
import os
import hashlib
from typing import Tuple, List, Dict


class WindowsPathLengthHandler:
    """Windows路径长度处理器"""
    
    # Windows默认最大路径长度
    MAX_PATH_LENGTH = 260
    # 保留一些字符用于文件扩展名等
    SAFE_PATH_LENGTH = 240
    # 最大文件名长度
    MAX_FILENAME_LENGTH = 255
    # 安全文件名长度
    SAFE_FILENAME_LENGTH = 200
    
    def __init__(self):
        self.path_mapping = {}  # 存储原始路径到短路径的映射
        self.reverse_mapping = {}  # 存储短路径到原始路径的映射
    
    @staticmethod
    def is_path_too_long(path: str) -> bool:
        """检查路径是否超过Windows限制"""
        return len(path) >= WindowsPathLengthHandler.MAX_PATH_LENGTH
    
    @staticmethod
    def is_filename_too_long(filename: str) -> bool:
        """检查文件名是否超过限制"""
        return len(filename) >= WindowsPathLengthHandler.MAX_FILENAME_LENGTH
    
    @staticmethod
    def truncate_filename(filename: str, max_length: int = None) -> str:
        """截断文件名到安全长度"""
        if max_length is None:
            max_length = WindowsPathLengthHandler.SAFE_FILENAME_LENGTH
        
        if len(filename) <= max_length:
            return filename
        
        # 分离文件名和扩展名
        name, ext = os.path.splitext(filename)
        
        # 计算可用于文件名的长度
        available_length = max_length - len(ext)
        
        if available_length <= 0:
            # 如果扩展名太长，只保留扩展名的一部分
            return filename[:max_length]
        
        # 截断文件名并添加哈希值以保持唯一性
        if available_length > 8:  # 为哈希值留出空间
            hash_suffix = hashlib.md5(filename.encode()).hexdigest()[:6]
            truncated_name = name[:available_length-7] + "_" + hash_suffix
        else:
            truncated_name = name[:available_length]
        
        return truncated_name + ext
    
    @staticmethod
    def create_short_path_component(component: str, max_length: int = 50) -> str:
        """创建短路径组件"""
        if len(component) <= max_length:
            return component
        
        # 创建短名称，保留开头和结尾，中间用哈希值
        if max_length > 16:
            hash_part = hashlib.md5(component.encode()).hexdigest()[:6]
            prefix_len = (max_length - 7) // 2
            suffix_len = max_length - 7 - prefix_len
            return component[:prefix_len] + "_" + hash_part + "_" + component[-suffix_len:]
        else:
            # 如果最大长度太小，只使用哈希值
            return hashlib.md5(component.encode()).hexdigest()[:max_length]
    
    def optimize_backup_path(self, base_backup_dir: str, source_path: str) -> Tuple[str, Dict[str, str]]:
        """
        优化备份路径结构以避免长路径问题
        
        Args:
            base_backup_dir: 基础备份目录
            source_path: 源路径
            
        Returns:
            (优化后的路径, 路径映射字典)
        """
        # 清理源路径
        source_path = source_path.rstrip('/')
        original_dirname = os.path.basename(source_path)
        
        # 创建短目录名
        if self.is_filename_too_long(original_dirname):
            short_dirname = self.create_short_path_component(original_dirname, 30)
        else:
            # 清理特殊字符
            short_dirname = "".join(c for c in original_dirname if c.isalnum() or c in ('-', '_', '.'))
            if not short_dirname:
                short_dirname = "backup_dir"
        
        # 构建优化后的路径
        optimized_path = os.path.join(base_backup_dir, short_dirname)
        
        # 检查路径长度
        if self.is_path_too_long(optimized_path):
            # 如果还是太长，进一步缩短
            short_dirname = self.create_short_path_component(original_dirname, 15)
            optimized_path = os.path.join(base_backup_dir, short_dirname)
        
        # 创建映射
        path_mapping = {
            'original_path': source_path,
            'original_dirname': original_dirname,
            'optimized_path': optimized_path,
            'short_dirname': short_dirname
        }
        
        # 存储映射
        self.path_mapping[source_path] = optimized_path
        self.reverse_mapping[optimized_path] = source_path
        
        return optimized_path, path_mapping
    
    def create_path_mapping_file(self, backup_session_dir: str, mappings: List[Dict[str, str]]):
        """创建路径映射文件"""
        mapping_file = os.path.join(backup_session_dir, "path_mappings.txt")
        
        try:
            with open(mapping_file, 'w', encoding='utf-8') as f:
                f.write("# 备份路径映射文件\n")
                f.write("# 格式: 原始路径 -> 备份路径\n\n")
                
                for mapping in mappings:
                    f.write(f"原始路径: {mapping['original_path']}\n")
                    f.write(f"原始目录名: {mapping['original_dirname']}\n")
                    f.write(f"备份路径: {mapping['optimized_path']}\n")
                    f.write(f"短目录名: {mapping['short_dirname']}\n")
                    f.write("-" * 50 + "\n")
                    
        except Exception as e:
            print(f"创建路径映射文件失败: {e}")
    
    @staticmethod
    def enable_long_path_support():
        """
        尝试启用Windows长路径支持
        注意：这需要Windows 10 1607+版本和管理员权限
        """
        try:
            import winreg
            
            # 尝试修改注册表启用长路径支持
            key_path = r"SYSTEM\CurrentControlSet\Control\FileSystem"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "LongPathsEnabled", 0, winreg.REG_DWORD, 1)
                return True, "长路径支持已启用（需要重启生效）"
                
        except Exception as e:
            return False, f"无法启用长路径支持: {e}"
    
    @staticmethod
    def check_long_path_support() -> bool:
        """检查系统是否支持长路径"""
        try:
            import winreg
            
            key_path = r"SYSTEM\CurrentControlSet\Control\FileSystem"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_READ) as key:
                value, _ = winreg.QueryValueEx(key, "LongPathsEnabled")
                return value == 1
                
        except Exception:
            return False
    
    def get_safe_extraction_path(self, base_path: str, relative_path: str) -> str:
        """
        获取安全的解压路径，避免路径过长
        
        Args:
            base_path: 基础路径
            relative_path: 相对路径
            
        Returns:
            安全的完整路径
        """
        # 分割相对路径
        path_parts = relative_path.split('/')
        safe_parts = []
        
        current_path = base_path
        
        for part in path_parts:
            # 处理每个路径组件
            if self.is_filename_too_long(part):
                safe_part = self.create_short_path_component(part, 30)
            else:
                safe_part = part
            
            safe_parts.append(safe_part)
            test_path = os.path.join(current_path, safe_part)
            
            # 检查路径长度
            if self.is_path_too_long(test_path):
                # 如果路径太长，使用更短的组件
                safe_part = self.create_short_path_component(part, 15)
                safe_parts[-1] = safe_part
                test_path = os.path.join(current_path, safe_part)
                
                # 如果还是太长，使用哈希值
                if self.is_path_too_long(test_path):
                    safe_part = hashlib.md5(part.encode()).hexdigest()[:10]
                    safe_parts[-1] = safe_part
            
            current_path = os.path.join(current_path, safe_part)
        
        return os.path.join(base_path, *safe_parts)


def test_path_length_handler():
    """测试路径长度处理器"""
    handler = WindowsPathLengthHandler()
    
    # 测试长路径
    long_path = "/very/long/path/with/many/components/that/might/exceed/windows/path/length/limits/especially/when/combined/with/backup/directory/structure"
    base_backup = "C:\\Backups\\backup_20250708_114433"
    
    optimized, mapping = handler.optimize_backup_path(base_backup, long_path)
    
    print(f"原始路径: {long_path}")
    print(f"优化路径: {optimized}")
    print(f"路径长度: {len(optimized)}")
    print(f"是否安全: {not handler.is_path_too_long(optimized)}")
    
    return handler


if __name__ == "__main__":
    test_path_length_handler()
