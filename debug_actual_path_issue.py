#!/usr/bin/env python3
"""
调试实际路径问题
分析具体的备份失败路径
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def analyze_actual_error():
    """分析实际的错误路径"""
    print("=== 分析实际错误路径 ===")
    
    # 实际的错误路径
    error_path = "G:\\tyy_backup\\backup_20250708_114433\\imagefile\\imagefile\\dolfinx\\volume-append\\jupyter-dolfinx \\Untitled.ipynb"
    
    print(f"错误路径: {error_path}")
    print(f"路径长度: {len(error_path)}")
    
    # 分析路径组件
    parts = error_path.split('\\')
    print(f"路径组件数量: {len(parts)}")
    
    for i, part in enumerate(parts):
        print(f"  {i}: '{part}' (长度: {len(part)})")
    
    # 检查问题
    print("\n问题分析:")
    
    # 1. 重复的imagefile目录
    if "imagefile\\imagefile" in error_path:
        print("❌ 发现重复的目录结构: imagefile\\imagefile")
    
    # 2. 文件名中的空格
    filename = os.path.basename(error_path)
    if ' ' in filename:
        print(f"❌ 文件名包含空格: '{filename}'")
    
    # 3. 路径中的空格
    if ' \\' in error_path or '\\ ' in error_path:
        print("❌ 路径中包含空格和反斜杠的组合")
    
    # 4. 检查是否存在
    print(f"\n文件是否存在: {os.path.exists(error_path)}")
    
    # 5. 检查父目录
    parent_dir = os.path.dirname(error_path)
    print(f"父目录: {parent_dir}")
    print(f"父目录是否存在: {os.path.exists(parent_dir)}")
    
    return error_path

def test_path_creation():
    """测试路径创建"""
    print("\n=== 测试路径创建 ===")
    
    try:
        from path_length_utils import WindowsPathLengthHandler
        
        handler = WindowsPathLengthHandler()
        
        # 模拟备份过程中的路径创建
        base_backup = "G:\\tyy_backup\\backup_20250708_114433"
        source_path = "/imagefile"
        
        # 第一次优化
        optimized_path, mapping = handler.optimize_backup_path(base_backup, source_path)
        print(f"第一次优化: {source_path} -> {optimized_path}")
        
        # 模拟tar文件中的相对路径
        tar_relative_path = "dolfinx/volume-append/jupyter-dolfinx /Untitled.ipynb"
        
        # 构建完整路径
        full_path = os.path.join(optimized_path, tar_relative_path)
        print(f"完整路径: {full_path}")
        print(f"路径长度: {len(full_path)}")
        
        # 使用安全解压路径
        safe_path = handler.get_safe_extraction_path(optimized_path, tar_relative_path)
        print(f"安全路径: {safe_path}")
        print(f"安全路径长度: {len(safe_path)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路径创建测试失败: {e}")
        return False

def test_space_handling():
    """测试空格处理"""
    print("\n=== 测试空格处理 ===")
    
    try:
        from path_length_utils import WindowsPathLengthHandler
        
        handler = WindowsPathLengthHandler()
        
        # 测试包含空格的路径
        paths_with_spaces = [
            "jupyter-dolfinx /Untitled.ipynb",
            "folder with spaces/file.txt",
            "path/with /trailing space/file.txt",
            "path/ with/leading space/file.txt"
        ]
        
        base_path = "C:\\test"
        
        for path in paths_with_spaces:
            safe_path = handler.get_safe_extraction_path(base_path, path)
            print(f"原路径: '{path}'")
            print(f"安全路径: '{safe_path}'")
            print("---")
        
        return True
        
    except Exception as e:
        print(f"❌ 空格处理测试失败: {e}")
        return False

def test_duplicate_directory_fix():
    """测试重复目录修复"""
    print("\n=== 测试重复目录修复 ===")
    
    try:
        from backup_manager import BackupManager
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        backup_manager = BackupManager(config_manager)
        
        # 模拟会导致重复目录的情况
        base_backup = "G:\\tyy_backup\\backup_20250708_114433"
        source_path = "/imagefile"
        
        # 使用备份管理器的路径优化
        optimized_path, mapping = backup_manager.path_handler.optimize_backup_path(base_backup, source_path)
        
        print(f"源路径: {source_path}")
        print(f"优化路径: {optimized_path}")
        print(f"映射信息: {mapping}")
        
        # 检查是否避免了重复目录
        path_parts = optimized_path.split('\\')
        duplicates = []
        for i, part in enumerate(path_parts):
            if part in path_parts[i+1:]:
                duplicates.append(part)
        
        if duplicates:
            print(f"❌ 仍然存在重复目录: {duplicates}")
            return False
        else:
            print("✅ 成功避免重复目录")
            return True
        
    except Exception as e:
        print(f"❌ 重复目录修复测试失败: {e}")
        return False

def suggest_manual_fix():
    """建议手动修复方案"""
    print("\n=== 建议手动修复方案 ===")
    
    print("基于分析，建议以下修复方案:")
    print("1. 在备份前检查并清理重复的目录结构")
    print("2. 处理文件名中的空格字符")
    print("3. 限制备份目录的嵌套深度")
    print("4. 使用更短的备份根目录名")
    
    # 建议的路径优化
    original_path = "G:\\tyy_backup\\backup_20250708_114433\\imagefile\\imagefile\\dolfinx\\volume-append\\jupyter-dolfinx \\Untitled.ipynb"
    
    # 修复建议
    fixes = [
        "移除重复的imagefile目录",
        "清理文件名中的空格",
        "使用更短的目录名",
        "减少嵌套层级"
    ]
    
    print(f"\n原问题路径: {original_path}")
    print("建议修复后的路径:")
    
    # 应用修复
    fixed_path = original_path.replace("\\imagefile\\imagefile\\", "\\imagefile\\")  # 移除重复
    fixed_path = fixed_path.replace("jupyter-dolfinx \\", "jupyter-dolfinx\\")  # 移除空格
    fixed_path = fixed_path.replace("volume-append", "vol-app")  # 缩短目录名
    
    print(f"修复后路径: {fixed_path}")
    print(f"原长度: {len(original_path)}")
    print(f"修复后长度: {len(fixed_path)}")
    
    return fixed_path

def main():
    """主函数"""
    print("开始调试实际路径问题...")
    print("=" * 60)
    
    # 分析实际错误
    error_path = analyze_actual_error()
    
    # 运行测试
    tests = [
        test_path_creation,
        test_space_handling,
        test_duplicate_directory_fix
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    # 提供修复建议
    suggest_manual_fix()
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    print("\n总结:")
    print("主要问题可能是:")
    print("1. 重复的目录结构 (imagefile\\imagefile)")
    print("2. 文件名中的空格处理")
    print("3. 可能的文件系统权限问题")
    print("4. tar解压时的路径处理问题")
    
    print("\n建议:")
    print("1. 检查tar文件的内部结构")
    print("2. 确保解压时正确处理路径")
    print("3. 使用更短的备份目录名")
    print("4. 在解压前预处理路径")

if __name__ == "__main__":
    main()
