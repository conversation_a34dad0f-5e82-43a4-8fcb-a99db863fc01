#!/usr/bin/env python3
"""
测试脚本 - 验证项目修改
测试路径处理、服务器间传输和备份功能
"""
import sys
import os

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_path_utils():
    """测试路径处理工具"""
    print("=== 测试路径处理工具 ===")
    
    try:
        from endpoints.base_endpoint import PathUtils
        
        # 测试本地路径标准化
        local_path = "C:\\Users\\<USER>\\Documents"
        normalized_local = PathUtils.normalize_local_path(local_path)
        print(f"本地路径标准化: {local_path} -> {normalized_local}")
        
        # 测试远程路径标准化
        remote_path = "/home/<USER>//documents/"
        normalized_remote = PathUtils.normalize_remote_path(remote_path)
        print(f"远程路径标准化: {remote_path} -> {normalized_remote}")
        
        # 测试路径连接
        local_joined = PathUtils.join_local_path("C:", "Users", "test")
        remote_joined = PathUtils.join_remote_path("/home", "user", "documents")
        print(f"本地路径连接: {local_joined}")
        print(f"远程路径连接: {remote_joined}")
        
        # 测试路径转换
        converted_to_remote = PathUtils.convert_path_for_transfer("C:\\test\\path", "local", "remote")
        converted_to_local = PathUtils.convert_path_for_transfer("/test/path", "remote", "local")
        print(f"Windows->Linux路径转换: C:\\test\\path -> {converted_to_remote}")
        print(f"Linux->Windows路径转换: /test/path -> {converted_to_local}")
        
        print("✅ 路径处理工具测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 路径处理工具测试失败: {e}")
        return False

def test_backup_manager():
    """测试备份管理器"""
    print("\n=== 测试备份管理器 ===")
    
    try:
        from backup_manager import BackupManager, BackupTask
        from config_manager import ConfigManager
        
        # 创建配置管理器和备份管理器
        config_manager = ConfigManager()
        backup_manager = BackupManager(config_manager)
        
        # 创建测试备份任务
        task = backup_manager.create_backup_task(
            name="测试备份任务",
            server_name="test_server",
            source_directories=["/home/<USER>/documents", "/home/<USER>/projects"],
            local_backup_root="C:\\Backups",
            use_compression=True
        )
        
        print(f"创建备份任务: {task.name}")
        print(f"任务ID: {task.task_id}")
        print(f"源目录数量: {len(task.source_directories)}")
        
        # 获取所有备份任务
        tasks = backup_manager.get_backup_tasks()
        print(f"总备份任务数: {len(tasks)}")
        
        # 删除测试任务
        backup_manager.delete_backup_task(task.task_id)
        print("测试任务已删除")
        
        print("✅ 备份管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 备份管理器测试失败: {e}")
        return False

def test_ssh_client_enhancements():
    """测试SSH客户端增强功能"""
    print("\n=== 测试SSH客户端增强功能 ===")
    
    try:
        from ssh_client import SSHClient
        
        # 创建SSH客户端实例
        ssh_client = SSHClient()
        
        # 检查新方法是否存在
        assert hasattr(ssh_client, 'execute_command'), "缺少execute_command方法"
        assert hasattr(ssh_client, 'transfer_to_remote_server'), "缺少transfer_to_remote_server方法"
        assert hasattr(ssh_client, '_transfer_compressed_directory'), "缺少_transfer_compressed_directory方法"
        assert hasattr(ssh_client, '_transfer_direct'), "缺少_transfer_direct方法"
        
        print("✅ SSH客户端增强功能检查通过")
        return True
        
    except Exception as e:
        print(f"❌ SSH客户端增强功能测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n=== 测试GUI组件 ===")
    
    try:
        # 测试备份面板导入
        from gui_v2.backup_panel import BackupPanel, BackupTaskDialog
        print("✅ 备份面板导入成功")
        
        # 测试主窗口导入
        from gui_v2.main_window import BackupToolGUIV2
        print("✅ 主窗口导入成功")
        
        # 测试端点面板中的RemoteDirectoryBrowser
        from gui_v2.endpoint_panel import RemoteDirectoryBrowser
        print("✅ 远程目录浏览器导入成功")
        
        print("✅ GUI组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
        return False

def test_transfer_coordinator():
    """测试传输协调器增强"""
    print("\n=== 测试传输协调器增强 ===")
    
    try:
        from transfer.transfer_coordinator import TransferCoordinator
        from endpoints.local_endpoint import LocalEndpoint
        
        coordinator = TransferCoordinator()
        local_endpoint = LocalEndpoint()
        local_endpoint.connect()
        
        # 检查路径标准化是否集成
        print("✅ 传输协调器导入成功")
        
        # 测试路径标准化
        test_path = "C:\\test\\path"
        normalized = local_endpoint.normalize_path(test_path)
        print(f"端点路径标准化测试: {test_path} -> {normalized}")
        
        print("✅ 传输协调器增强测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 传输协调器增强测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试项目修改...")
    print("=" * 50)
    
    tests = [
        test_path_utils,
        test_backup_manager,
        test_ssh_client_enhancements,
        test_gui_components,
        test_transfer_coordinator
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目修改成功。")
        print("\n主要功能:")
        print("1. ✅ 路径处理优化 - 区分Windows本地路径和Linux远程路径")
        print("2. ✅ 服务器间直接传输 - 支持真正的服务器到服务器传输")
        print("3. ✅ 服务器备份功能 - 完整的备份任务管理系统")
        print("\n可以运行 'python main.py' 启动程序进行实际测试。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
