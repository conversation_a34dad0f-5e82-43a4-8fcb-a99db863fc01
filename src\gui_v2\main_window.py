"""
远程文件备份工具 V2.0 主窗口
支持源端和目标端的灵活配置
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import os

try:
    from ..config_manager import ConfigManager
    from ..endpoints.local_endpoint import LocalEndpoint
    from ..endpoints.remote_endpoint import RemoteEndpoint
    from ..transfer.transfer_coordinator import TransferCoordinator
    from ..transfer.transfer_strategies import StrategyFactory
    from .endpoint_panel import EndpointPanel
    from .transfer_panel import TransferPanel
    from .backup_panel import BackupPanel
except ImportError:
    from config_manager import ConfigManager
    from endpoints.local_endpoint import LocalEndpoint
    from endpoints.remote_endpoint import RemoteEndpoint
    from transfer.transfer_coordinator import TransferCoordinator
    from transfer.transfer_strategies import StrategyFactory
    from gui_v2.endpoint_panel import EndpointPanel
    from gui_v2.transfer_panel import TransferPanel
    from gui_v2.backup_panel import BackupPanel


class BackupToolGUIV2:
    """备份工具 V2.0 图形界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("远程文件备份工具 V2.0")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.transfer_coordinator = TransferCoordinator()
        
        # 端点实例
        self.local_endpoint = LocalEndpoint()
        self.local_endpoint.connect()
        
        # 界面变量
        self.current_transfer = None
        self.transfer_thread = None
        
        # 创建界面
        self.create_widgets()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="远程文件备份工具 V2.0",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))

        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件传输选项卡
        self.transfer_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.transfer_tab, text="文件传输")
        self.create_transfer_tab()

        # 服务器备份选项卡
        self.backup_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.backup_tab, text="服务器备份")
        self.create_backup_tab()

        # 服务器管理按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=(10, 0))
        ttk.Button(button_frame, text="服务器管理", command=self.open_server_manager).pack()

    def create_transfer_tab(self):
        """创建文件传输选项卡"""
        # 配置网格权重
        self.transfer_tab.columnconfigure(0, weight=1)
        self.transfer_tab.columnconfigure(1, weight=1)
        self.transfer_tab.rowconfigure(0, weight=1)

        # 端点配置区域
        endpoints_frame = ttk.Frame(self.transfer_tab)
        endpoints_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 10))
        endpoints_frame.columnconfigure(0, weight=1)
        endpoints_frame.columnconfigure(1, weight=1)
        endpoints_frame.rowconfigure(0, weight=1)

        # 源端点面板
        self.source_panel = EndpointPanel(
            endpoints_frame,
            "源端点",
            self.config_manager,
            self.on_source_endpoint_changed
        )
        self.source_panel.frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # 目标端点面板
        self.target_panel = EndpointPanel(
            endpoints_frame,
            "目标端点",
            self.config_manager,
            self.on_target_endpoint_changed
        )
        self.target_panel.frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # 传输控制面板
        self.transfer_panel = TransferPanel(self.transfer_tab, self.on_transfer_settings_changed)
        self.transfer_panel.frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 进度显示区域
        self.create_progress_section(self.transfer_tab, 2)

        # 日志显示区域
        self.create_log_section(self.transfer_tab, 3)

        # 控制按钮区域
        self.create_control_section(self.transfer_tab, 4)

        # 刷新服务器列表
        self.refresh_server_lists()

    def create_backup_tab(self):
        """创建服务器备份选项卡"""
        self.backup_tab.columnconfigure(0, weight=1)
        self.backup_tab.rowconfigure(0, weight=1)

        # 创建备份面板
        self.backup_panel = BackupPanel(self.backup_tab, self.config_manager)
        self.backup_panel.frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
    
    def create_progress_section(self, parent, row):
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text="传输进度", padding="5")
        progress_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 进度文本
        self.progress_label = ttk.Label(progress_frame, text="就绪")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
        
        # 传输信息
        self.transfer_info_label = ttk.Label(progress_frame, text="", foreground="blue")
        self.transfer_info_label.grid(row=2, column=0, sticky=tk.W)
    
    def create_log_section(self, parent, row):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
    
    def create_control_section(self, parent, row):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # 左侧按钮
        left_buttons = ttk.Frame(control_frame)
        left_buttons.pack(side=tk.LEFT)
        
        self.analyze_button = ttk.Button(left_buttons, text="分析传输", command=self.analyze_transfer)
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.start_button = ttk.Button(left_buttons, text="开始传输", command=self.start_transfer)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_button = ttk.Button(left_buttons, text="取消传输", 
                                       command=self.cancel_transfer, state="disabled")
        self.cancel_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 右侧按钮
        right_buttons = ttk.Frame(control_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(10, 0))
    
    def refresh_server_lists(self):
        """刷新服务器列表"""
        if hasattr(self, 'source_panel') and hasattr(self, 'target_panel'):
            servers = self.config_manager.list_servers()
            self.source_panel.update_server_list(servers)
            self.target_panel.update_server_list(servers)
    
    def on_source_endpoint_changed(self, endpoint_type, server_name, path):
        """源端点变化回调"""
        if hasattr(self, 'log_text'):
            self.log_message(f"源端点已设置: {endpoint_type} - {path}")
        self.update_transfer_info()

    def on_target_endpoint_changed(self, endpoint_type, server_name, path):
        """目标端点变化回调"""
        if hasattr(self, 'log_text'):
            self.log_message(f"目标端点已设置: {endpoint_type} - {path}")
        self.update_transfer_info()

    def on_transfer_settings_changed(self, settings):
        """传输设置变化回调"""
        if hasattr(self, 'log_text'):
            self.log_message(f"传输设置已更新: 压缩={settings.get('use_compression', False)}")
        self.update_transfer_info()
    
    def update_transfer_info(self):
        """更新传输信息显示"""
        try:
            # 检查必要的属性是否存在
            if not hasattr(self, 'source_panel') or not hasattr(self, 'target_panel'):
                return

            source_info = self.source_panel.get_endpoint_info()
            target_info = self.target_panel.get_endpoint_info()

            # 检查是否有传输信息标签
            if hasattr(self, 'transfer_info_label'):
                if source_info and target_info:
                    info_text = f"{source_info['type']} → {target_info['type']}"
                    if source_info.get('server_name'):
                        info_text = info_text.replace(source_info['type'],
                                                    f"{source_info['type']}({source_info['server_name']})")
                    if target_info.get('server_name'):
                        info_text = info_text.replace(target_info['type'],
                                                    f"{target_info['type']}({target_info['server_name']})")

                    self.transfer_info_label.config(text=info_text)
                else:
                    self.transfer_info_label.config(text="")
        except Exception as e:
            print(f"更新传输信息失败: {e}")
    
    def analyze_transfer(self):
        """分析传输"""
        try:
            # 获取传输计划
            plan = self.create_transfer_plan()
            if not plan:
                return
            
            # 分析传输
            analysis = StrategyFactory.analyze_transfer(plan)
            
            # 显示分析结果
            self.show_transfer_analysis(analysis)
            
        except Exception as e:
            messagebox.showerror("错误", f"分析传输失败: {str(e)}")
            self.log_message(f"分析传输失败: {str(e)}")
    
    def create_transfer_plan(self):
        """创建传输计划"""
        try:
            # 获取源端点
            source_info = self.source_panel.get_endpoint_info()
            if not source_info:
                messagebox.showwarning("警告", "请配置源端点")
                return None
            
            source_endpoint = self.get_endpoint_instance(source_info)
            if not source_endpoint or not source_endpoint.is_connected():
                messagebox.showerror("错误", "源端点连接失败")
                return None
            
            # 获取目标端点
            target_info = self.target_panel.get_endpoint_info()
            if not target_info:
                messagebox.showwarning("警告", "请配置目标端点")
                return None
            
            target_endpoint = self.get_endpoint_instance(target_info)
            if not target_endpoint or not target_endpoint.is_connected():
                messagebox.showerror("错误", "目标端点连接失败")
                return None
            
            # 获取传输设置
            transfer_settings = self.transfer_panel.get_settings()
            
            # 记录路径信息
            self.log_message(f"源路径: {source_info['path']}")
            self.log_message(f"目标路径: {target_info['path']}")
            self.log_message(f"使用压缩: {transfer_settings.get('use_compression', True)}")

            # 创建传输计划
            plan = self.transfer_coordinator.plan_transfer(
                source_endpoint, source_info['path'],
                target_endpoint, target_info['path'],
                transfer_settings.get('use_compression', True)
            )

            return plan
            
        except Exception as e:
            messagebox.showerror("错误", f"创建传输计划失败: {str(e)}")
            return None
    
    def get_endpoint_instance(self, endpoint_info):
        """获取端点实例"""
        if endpoint_info['type'] == 'local':
            return self.local_endpoint
        elif endpoint_info['type'] == 'remote':
            server_name = endpoint_info.get('server_name')
            if not server_name:
                return None
            
            remote_endpoint = RemoteEndpoint(server_name, self.config_manager)
            success, message = remote_endpoint.connect()
            if not success:
                self.log_message(f"连接远程端点失败: {message}")
                return None
            
            return remote_endpoint
        else:
            return None

    def show_transfer_analysis(self, analysis):
        """显示传输分析结果"""
        result_window = tk.Toplevel(self.root)
        result_window.title("传输分析结果")
        result_window.geometry("500x400")
        result_window.transient(self.root)
        result_window.grab_set()

        # 居中显示
        result_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        main_frame = ttk.Frame(result_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 验证结果
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        status_color = "green" if analysis['valid'] else "red"
        status_text = "✓ 验证通过" if analysis['valid'] else "✗ 验证失败"
        ttk.Label(status_frame, text="验证状态:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        ttk.Label(status_frame, text=status_text, foreground=status_color).pack(side=tk.LEFT, padx=(10, 0))

        # 消息
        if analysis['message']:
            ttk.Label(main_frame, text=f"消息: {analysis['message']}",
                     wraplength=450).pack(fill=tk.X, pady=(0, 10))

        # 估算时间
        estimated_time = analysis['estimated_time']
        time_text = self.format_time(estimated_time)
        ttk.Label(main_frame, text=f"估算时间: {time_text}",
                 font=("Arial", 10, "bold")).pack(fill=tk.X, pady=(0, 10))

        # 建议
        if analysis['recommendations']:
            ttk.Label(main_frame, text="建议:", font=("Arial", 10, "bold")).pack(fill=tk.X, pady=(10, 5))
            for rec in analysis['recommendations']:
                ttk.Label(main_frame, text=f"• {rec}", wraplength=450).pack(fill=tk.X, pady=(0, 2))

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        if analysis['valid']:
            ttk.Button(button_frame, text="开始传输",
                      command=lambda: [result_window.destroy(), self.start_transfer()]).pack(side=tk.LEFT)

        ttk.Button(button_frame, text="关闭",
                  command=result_window.destroy).pack(side=tk.RIGHT)

    def format_time(self, seconds):
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds} 秒"
        elif seconds < 3600:
            minutes = seconds // 60
            secs = seconds % 60
            return f"{minutes} 分 {secs} 秒"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours} 小时 {minutes} 分"

    def start_transfer(self):
        """开始传输"""
        try:
            # 创建传输计划
            plan = self.create_transfer_plan()
            if not plan:
                return

            # 分析传输计划
            analysis = StrategyFactory.analyze_transfer(plan)
            if not analysis['valid']:
                messagebox.showerror("错误", f"传输验证失败: {analysis['message']}")
                return

            # 使用优化后的计划
            optimized_plan = analysis['optimized_plan']

            # 显示传输信息
            transfer_info = self.transfer_coordinator.get_transfer_info(optimized_plan)
            self.log_message(f"开始传输: {transfer_info['mode_description']}")
            self.log_message(f"源: {transfer_info['source_name']} - {transfer_info['source_path']}")
            self.log_message(f"目标: {transfer_info['target_name']} - {transfer_info['target_path']}")
            self.log_message(f"压缩: {'启用' if transfer_info['use_compression'] else '禁用'}")

            # 禁用开始按钮，启用取消按钮
            self.start_button.config(state="disabled")
            self.analyze_button.config(state="disabled")
            self.cancel_button.config(state="normal")

            # 重置进度
            self.progress_var.set(0)
            self.progress_label.config(text="准备传输...")

            # 保存当前传输
            self.current_transfer = optimized_plan

            # 开始传输线程
            self.transfer_thread = threading.Thread(
                target=self._transfer_worker,
                args=(optimized_plan,),
                daemon=True
            )
            self.transfer_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动传输失败: {str(e)}")
            self.log_message(f"启动传输失败: {str(e)}")

    def _transfer_worker(self, plan):
        """传输工作线程"""
        try:
            def progress_callback(current, total, message=""):
                progress = (current / total) * 100 if total > 0 else 0
                self.root.after(0, lambda: self.update_progress(progress, message))

            # 执行传输
            success, message = self.transfer_coordinator.execute_transfer(plan, progress_callback)

            # 更新UI
            self.root.after(0, lambda: self.on_transfer_complete(success, message))

        except Exception as e:
            self.root.after(0, lambda: self.on_transfer_complete(False, f"传输异常: {str(e)}"))

    def update_progress(self, progress, message):
        """更新进度"""
        self.progress_var.set(progress)
        if message:
            self.progress_label.config(text=f"进度: {progress:.1f}% - {message}")
        else:
            self.progress_label.config(text=f"进度: {progress:.1f}%")

    def on_transfer_complete(self, success, message):
        """传输完成回调"""
        # 恢复按钮状态
        self.start_button.config(state="normal")
        self.analyze_button.config(state="normal")
        self.cancel_button.config(state="disabled")

        if success:
            self.progress_var.set(100)
            self.progress_label.config(text="传输完成")
            self.log_message(f"传输成功: {message}")
            messagebox.showinfo("成功", "传输完成")
        else:
            self.progress_label.config(text="传输失败")
            self.log_message(f"传输失败: {message}")
            messagebox.showerror("失败", f"传输失败: {message}")

        self.current_transfer = None
        self.transfer_thread = None

    def cancel_transfer(self):
        """取消传输"""
        if self.current_transfer:
            self.transfer_coordinator.cancel_current_transfer()
            self.log_message("正在取消传输...")

    def log_message(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_server_manager(self):
        """打开服务器管理窗口"""
        try:
            from .server_manager import ServerManagerWindow

            # 创建服务器管理窗口
            ServerManagerWindow(self.root, self.config_manager, self.refresh_server_lists)

        except Exception as e:
            messagebox.showerror("错误", f"打开服务器管理失败: {str(e)}")

    def on_closing(self):
        """关闭程序"""
        if self.current_transfer:
            if messagebox.askyesno("确认", "正在传输中，确定要退出吗？"):
                self.cancel_transfer()
            else:
                return

        # 断开所有连接
        try:
            if hasattr(self, 'local_endpoint'):
                self.local_endpoint.disconnect()
        except:
            pass

        self.root.destroy()

    def run(self):
        """运行程序"""
        self.root.mainloop()
