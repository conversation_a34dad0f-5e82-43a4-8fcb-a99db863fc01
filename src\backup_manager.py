"""
备份管理器
管理服务器备份任务的创建、执行和历史记录
"""
import os
import json
import time
from datetime import datetime
from typing import List, Dict, Optional, Callable, Tuple

try:
    from .endpoints.remote_endpoint import RemoteEndpoint
    from .endpoints.local_endpoint import LocalEndpoint
    from .transfer.transfer_coordinator import TransferCoordinator
    from .config_manager import ConfigManager
except ImportError:
    from endpoints.remote_endpoint import RemoteEndpoint
    from endpoints.local_endpoint import LocalEndpoint
    from transfer.transfer_coordinator import TransferCoordinator
    from config_manager import ConfigManager


class BackupTask:
    """备份任务类"""
    
    def __init__(self, task_id: str = None):
        self.task_id = task_id or f"backup_{int(time.time())}"
        self.name = ""
        self.server_name = ""
        self.source_directories = []  # 要备份的服务器目录列表
        self.local_backup_root = ""   # 本地备份根目录
        self.use_compression = True
        self.enabled = True
        self.created_time = datetime.now().isoformat()
        self.last_run_time = None
        self.last_run_status = None
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'server_name': self.server_name,
            'source_directories': self.source_directories,
            'local_backup_root': self.local_backup_root,
            'use_compression': self.use_compression,
            'enabled': self.enabled,
            'created_time': self.created_time,
            'last_run_time': self.last_run_time,
            'last_run_status': self.last_run_status
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'BackupTask':
        """从字典创建"""
        task = cls(data.get('task_id'))
        task.name = data.get('name', '')
        task.server_name = data.get('server_name', '')
        task.source_directories = data.get('source_directories', [])
        task.local_backup_root = data.get('local_backup_root', '')
        task.use_compression = data.get('use_compression', True)
        task.enabled = data.get('enabled', True)
        task.created_time = data.get('created_time', datetime.now().isoformat())
        task.last_run_time = data.get('last_run_time')
        task.last_run_status = data.get('last_run_status')
        return task


class BackupManager:
    """备份管理器"""
    
    def __init__(self, config_manager: ConfigManager = None):
        self.config_manager = config_manager or ConfigManager()
        self.transfer_coordinator = TransferCoordinator()
        self.local_endpoint = LocalEndpoint()
        self.local_endpoint.connect()
        
        # 备份任务存储文件
        self.backup_tasks_file = "backup_tasks.json"
        self.backup_history_file = "backup_history.json"
        
        # 加载备份任务
        self.backup_tasks = self._load_backup_tasks()
    
    def _load_backup_tasks(self) -> Dict[str, BackupTask]:
        """加载备份任务"""
        tasks = {}
        try:
            if os.path.exists(self.backup_tasks_file):
                with open(self.backup_tasks_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for task_data in data.get('tasks', []):
                        task = BackupTask.from_dict(task_data)
                        tasks[task.task_id] = task
        except Exception as e:
            print(f"加载备份任务失败: {e}")
        return tasks
    
    def _save_backup_tasks(self):
        """保存备份任务"""
        try:
            data = {
                'tasks': [task.to_dict() for task in self.backup_tasks.values()]
            }
            with open(self.backup_tasks_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存备份任务失败: {e}")
    
    def create_backup_task(self, name: str, server_name: str, source_directories: List[str],
                          local_backup_root: str, use_compression: bool = True) -> BackupTask:
        """创建备份任务"""
        task = BackupTask()
        task.name = name
        task.server_name = server_name
        task.source_directories = source_directories
        task.local_backup_root = local_backup_root
        task.use_compression = use_compression
        
        self.backup_tasks[task.task_id] = task
        self._save_backup_tasks()
        return task
    
    def get_backup_tasks(self) -> List[BackupTask]:
        """获取所有备份任务"""
        return list(self.backup_tasks.values())
    
    def get_backup_task(self, task_id: str) -> Optional[BackupTask]:
        """获取指定备份任务"""
        return self.backup_tasks.get(task_id)
    
    def update_backup_task(self, task: BackupTask):
        """更新备份任务"""
        self.backup_tasks[task.task_id] = task
        self._save_backup_tasks()
    
    def delete_backup_task(self, task_id: str) -> bool:
        """删除备份任务"""
        if task_id in self.backup_tasks:
            del self.backup_tasks[task_id]
            self._save_backup_tasks()
            return True
        return False
    
    def execute_backup_task(self, task_id: str, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """执行备份任务"""
        task = self.get_backup_task(task_id)
        if not task:
            return False, "备份任务不存在"
        
        if not task.enabled:
            return False, "备份任务已禁用"
        
        try:
            # 创建时间戳目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_session_dir = os.path.join(task.local_backup_root, f"backup_{timestamp}")
            
            if not self.local_endpoint.create_directory(backup_session_dir):
                return False, f"创建备份目录失败: {backup_session_dir}"
            
            # 连接到远程服务器
            remote_endpoint = RemoteEndpoint(task.server_name, self.config_manager)
            success, message = remote_endpoint.connect()
            if not success:
                return False, f"连接服务器失败: {message}"
            
            total_dirs = len(task.source_directories)
            success_count = 0
            
            for i, source_dir in enumerate(task.source_directories):
                if progress_callback:
                    progress_callback(
                        (i / total_dirs) * 100,
                        100,
                        f"备份目录 {i+1}/{total_dirs}: {source_dir}"
                    )

                # 标准化源目录路径
                source_dir = source_dir.rstrip('/')

                # 为每个源目录创建对应的备份目录，避免重复目录结构
                dir_name = os.path.basename(source_dir)
                if not dir_name:  # 处理根目录情况
                    dir_name = "root"

                # 清理目录名中的特殊字符，避免路径问题
                safe_dir_name = "".join(c for c in dir_name if c.isalnum() or c in ('-', '_', '.'))
                if not safe_dir_name:
                    safe_dir_name = f"dir_{i+1}"

                target_dir = os.path.join(backup_session_dir, safe_dir_name)
                
                # 创建传输计划
                plan = self.transfer_coordinator.plan_transfer(
                    remote_endpoint, source_dir,
                    self.local_endpoint, target_dir,
                    task.use_compression
                )
                
                # 执行传输
                success, transfer_message = self.transfer_coordinator.execute_transfer(
                    plan, 
                    lambda c, t, m="": progress_callback(
                        ((i + c/t) / total_dirs) * 100, 
                        100, 
                        f"备份 {dir_name}: {m}"
                    ) if progress_callback else None
                )
                
                if success:
                    success_count += 1
                else:
                    print(f"备份目录失败 {source_dir}: {transfer_message}")
            
            # 更新任务状态
            task.last_run_time = datetime.now().isoformat()
            task.last_run_status = f"成功备份 {success_count}/{total_dirs} 个目录"
            self.update_backup_task(task)
            
            # 记录备份历史
            self._record_backup_history(task, backup_session_dir, success_count, total_dirs)
            
            if progress_callback:
                progress_callback(100, 100, "备份完成")
            
            remote_endpoint.disconnect()
            
            if success_count == total_dirs:
                return True, f"备份完成，所有 {total_dirs} 个目录备份成功"
            else:
                return True, f"备份完成，{success_count}/{total_dirs} 个目录备份成功"
                
        except Exception as e:
            task.last_run_time = datetime.now().isoformat()
            task.last_run_status = f"备份失败: {str(e)}"
            self.update_backup_task(task)
            return False, f"备份执行失败: {str(e)}"
    
    def _record_backup_history(self, task: BackupTask, backup_dir: str, success_count: int, total_count: int):
        """记录备份历史"""
        try:
            history_entry = {
                'task_id': task.task_id,
                'task_name': task.name,
                'server_name': task.server_name,
                'backup_time': datetime.now().isoformat(),
                'backup_directory': backup_dir,
                'success_count': success_count,
                'total_count': total_count,
                'status': 'success' if success_count == total_count else 'partial'
            }
            
            history = []
            if os.path.exists(self.backup_history_file):
                with open(self.backup_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            history.append(history_entry)
            
            # 只保留最近100条记录
            if len(history) > 100:
                history = history[-100:]
            
            with open(self.backup_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"记录备份历史失败: {e}")
    
    def get_backup_history(self) -> List[Dict]:
        """获取备份历史"""
        try:
            if os.path.exists(self.backup_history_file):
                with open(self.backup_history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"读取备份历史失败: {e}")
        return []
